package org.sounfury.aki.infrastructure.shared.config;

import org.sounfury.aki.domain.shared.llm.LlmClient;
import org.sounfury.aki.infrastructure.shared.llm.SpringAiLlmClientAdapter;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 领域层配置
 * 配置领域层接口的基础设施实现
 */
@Configuration
@EnableAsync
public class DomainConfig {
    
    /**
     * 配置LLM客户端实现
     */
    @Bean
    public LlmClient llmClient(ChatClient chatClient) {
        return new SpringAiLlmClientAdapter(chatClient);
    }
}
