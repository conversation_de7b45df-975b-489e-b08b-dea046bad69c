package org.sounfury.aki.infrastructure.shared.llm;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.shared.llm.LlmAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Spring AI Advisor桥接器
 * 将领域层的LlmAdvisor适配到Spring AI的Advisor接口
 * 简化设计，专注于适配逻辑
 */
@Slf4j
@Component
public class SpringAiAdvisorBridge {

    /**
     * 将领域Advisor列表适配为Spring AI Advisor列表
     * @param domainAdvisors 领域层Advisor列表
     * @return Spring AI Advisor列表
     */
    public List<Advisor> adaptToSpringAi(List<LlmAdvisor> domainAdvisors) {
        log.debug("适配 {} 个领域Advisor到Spring AI", domainAdvisors.size());

        // 简化：直接返回空列表，具体适配逻辑在具体Advisor实现中处理
        // 这里主要是为了保持接口一致性
        return List.of();
    }
}
