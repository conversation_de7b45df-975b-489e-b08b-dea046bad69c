package org.sounfury.aki.infrastructure.shared.llm;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.shared.llm.AdvisorRegistry;
import org.sounfury.aki.domain.shared.llm.LlmAdvisor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 默认Advisor注册器实现
 * 使用内存存储管理Advisor实例
 */
@Slf4j
@Component
public class DefaultAdvisorRegistry implements AdvisorRegistry {
    
    private final Map<String, LlmAdvisor> advisors = new ConcurrentHashMap<>();
    private final Map<String, AdvisorType> advisorTypes = new ConcurrentHashMap<>();
    
    @Override
    public void register(LlmAdvisor advisor) {
        String name = advisor.getName();
        advisors.put(name, advisor);
        
        // 根据Advisor名称推断类型
        AdvisorType type = inferAdvisorType(name);
        advisorTypes.put(name, type);
        
        log.info("注册Advisor: {} (类型: {}, 顺序: {})", name, type, advisor.getOrder());
    }
    
    @Override
    public void registerAll(List<LlmAdvisor> advisorList) {
        advisorList.forEach(this::register);
        log.info("批量注册 {} 个Advisor", advisorList.size());
    }
    
    @Override
    public Optional<LlmAdvisor> getAdvisor(String name) {
        return Optional.ofNullable(advisors.get(name));
    }
    
    @Override
    public List<LlmAdvisor> getAdvisorsByType(AdvisorType type) {
        return advisorTypes.entrySet().stream()
                .filter(entry -> entry.getValue() == type)
                .map(entry -> advisors.get(entry.getKey()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<LlmAdvisor> getAllAdvisors() {
        return advisors.values().stream()
                .sorted((a1, a2) -> Integer.compare(a1.getOrder(), a2.getOrder()))
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean isRegistered(String name) {
        return advisors.containsKey(name);
    }
    
    @Override
    public void unregister(String name) {
        LlmAdvisor removed = advisors.remove(name);
        advisorTypes.remove(name);
        
        if (removed != null) {
            log.info("注销Advisor: {}", name);
        } else {
            log.warn("尝试注销不存在的Advisor: {}", name);
        }
    }
    
    /**
     * 根据Advisor名称推断类型
     * @param name Advisor名称
     * @return 推断的类型
     */
    private AdvisorType inferAdvisorType(String name) {
        String lowerName = name.toLowerCase();
        
        if (lowerName.contains("system") || lowerName.contains("prompt")) {
            return AdvisorType.SYSTEM_PROMPT;
        } else if (lowerName.contains("character") || lowerName.contains("persona")) {
            return AdvisorType.CHARACTER;
        } else if (lowerName.contains("memory") || lowerName.contains("conversation")) {
            return AdvisorType.MEMORY;
        } else if (lowerName.contains("rag") || lowerName.contains("knowledge") || lowerName.contains("worldbook")) {
            return AdvisorType.RAG;
        } else if (lowerName.contains("tool") || lowerName.contains("function")) {
            return AdvisorType.TOOL;
        } else if (lowerName.contains("behavior") || lowerName.contains("guide")) {
            return AdvisorType.BEHAVIOR;
        } else {
            return AdvisorType.CUSTOM;
        }
    }
}
