package org.sounfury.aki.infrastructure.llm;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.model.aggregate.ModelConfiguration;
import org.sounfury.aki.domain.model.valueobject.ModelProvider;
import org.sounfury.aki.domain.model.valueobject.ModelSettings;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 可配置的聊天模型工厂
 * 根据LLM配置动态创建和管理ChatModel实例
 */
@Slf4j
@Component
public class ConfigurableChatModelFactory {

    /**
     * 模型实例缓存
     * Key: 配置ID, Value: ChatModel实例
     */
    private final Map<String, ChatModel> modelCache = new ConcurrentHashMap<>();

    /**deepseek-ai/DeepSeek-V3
     * 根据配置创建或获取ChatModel实例
     *
     * @param configuration LLM配置
     * @return ChatModel实例
     */
    public ChatModel createOrGetChatModel(ModelConfiguration configuration) {
        if (configuration == null || !configuration.isValid()) {
            throw new IllegalArgumentException("无效的LLM配置");
        }

        String configId = configuration.getConfigurationId();

        // 检查缓存中是否已存在
        ChatModel cachedModel = modelCache.get(configId);
        if (cachedModel != null) {
            log.debug("从缓存获取ChatModel: {}", configId);
            return cachedModel;
        }

        // 创建新的模型实例
        ChatModel newModel = createChatModel(configuration);
        modelCache.put(configId, newModel);

        log.info("创建新的ChatModel实例: ConfigId={}, Provider={}, Model={}",
                 configId,
                 configuration
                         .getProvider()
                         .getDisplayName(),
                 configuration
                         .getProvider()
                         .getModelName());

        return newModel;
    }

    /**
     * 根据配置创建ChatModel实例
     * 统一使用OpenAI规范接口
     */
    private ChatModel createChatModel(ModelConfiguration configuration) {
        return createUnifiedChatModel(configuration.getProvider(), configuration.getSettings());
    }

    /**
     * 创建统一的ChatModel实例
     * 使用OpenAI规范接口支持所有提供商
     */
    private ChatModel createUnifiedChatModel(ModelProvider provider, ModelSettings settings) {
        try {
            // 构建OpenAI API实例
            OpenAiApi openAiApi = OpenAiApi.builder()
                    .baseUrl(provider.getBaseUrl())
                    .apiKey(provider.getApiKey())
                    .build();

            // 构建ChatOptions
            OpenAiChatOptions chatOptions = buildChatOptions(provider, settings);

            // 创建ChatModel
            OpenAiChatModel chatModel = OpenAiChatModel.builder()
                    .openAiApi(openAiApi)
                    .defaultOptions(chatOptions)
                    .build();
            log.info("创建统一ChatModel: Provider={}, BaseUrl={}, Model={}",
                      provider.getType().getDisplayName(),
                      provider.getBaseUrl(),
                      provider.getModelName());

            return chatModel;

        } catch (Exception e) {
            log.error("创建ChatModel失败: Provider={}, Error={}",
                      provider.getType().getDisplayName(), e.getMessage(), e);
            throw new RuntimeException("创建ChatModel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建OpenAI ChatOptions
     * 将ModelSettings映射到OpenAI规范参数
     */
    private OpenAiChatOptions buildChatOptions(ModelProvider provider, ModelSettings settings) {
        OpenAiChatOptions.Builder optionsBuilder = OpenAiChatOptions.builder()
                .model(provider.getModelName());

        if (settings != null) {
            // 映射基本参数
            if (settings.getMaxTokens() != null) {
                optionsBuilder.maxTokens(settings.getMaxTokens());
            }
            if (settings.getTemperature() != null) {
                optionsBuilder.temperature(settings.getTemperature());
            }
            if (settings.getTopP() != null) {
                optionsBuilder.topP(settings.getTopP());
            }
            if (settings.getFrequencyPenalty() != null) {
                optionsBuilder.frequencyPenalty(settings.getFrequencyPenalty());
            }
            if (settings.getPresencePenalty() != null) {
                optionsBuilder.presencePenalty(settings.getPresencePenalty());
            }
            if (settings.getStopSequences() != null && settings.getStopSequences().length > 0) {
                optionsBuilder.stop(java.util.List.of(settings.getStopSequences()));
            }
        }

        return optionsBuilder.build();
    }

    /**
     * 移除缓存中的模型实例
     *
     * @param configurationId 配置ID
     */
    public void removeCachedModel(String configurationId) {
        ChatModel removed = modelCache.remove(configurationId);
        if (removed != null) {
            log.info("移除缓存的ChatModel: {}", configurationId);
        }
    }

    /**
     * 清空所有缓存的模型实例
     */
    public void clearAllCachedModels() {
        int count = modelCache.size();
        modelCache.clear();
        log.info("清空所有缓存的ChatModel，共{}个实例", count);
    }

    /**
     * 获取缓存的模型数量
     */
    public int getCachedModelCount() {
        return modelCache.size();
    }

    /**
     * 检查指定配置的模型是否已缓存
     */
    public boolean isModelCached(String configurationId) {
        return modelCache.containsKey(configurationId);
    }

    /**
     * 强制重新创建模型实例（清除缓存后重新创建）
     */
    public ChatModel recreateChatModel(ModelConfiguration configuration) {
        String configId = configuration.getConfigurationId();

        // 移除旧的缓存实例
        removeCachedModel(configId);

        // 创建新实例
        return createOrGetChatModel(configuration);
    }
}
