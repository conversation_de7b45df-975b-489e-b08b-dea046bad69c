package org.sounfury.aki.infrastructure.llmconfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.model.aggregate.ModelConfiguration;
import org.sounfury.aki.domain.model.event.ModelConfigurationChangedEvent;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 动态ChatClient管理器
 * 基础设施层负责根据配置变更动态创建和管理ChatClient实例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicChatClientManager {
    
    private final ConcurrentMap<String, ChatClient> chatClientCache = new ConcurrentHashMap<>();
    private volatile String currentConfigurationId = "global-llm-config";
    
    /**
     * 获取当前活跃的ChatClient
     * @return 当前配置对应的ChatClient实例
     */
    public ChatClient getCurrentChatClient() {
        return chatClientCache.computeIfAbsent(currentConfigurationId, this::createChatClientForConfiguration);
    }
    
    /**
     * 监听配置变更事件，动态重建ChatClient
     * @param event 配置变更事件
     */
    @EventListener
    public void handleConfigurationChanged(ModelConfigurationChangedEvent event) {
        log.info("收到LLM配置变更事件: {}", event.getChangeSummary());
        
        String configId = event.getConfigurationId();
        
        // 如果需要重建ChatClient
        if (event.requiresChatClientRebuild()) {
            log.info("重建ChatClient: {}", configId);
            
            // 移除旧的缓存
            ChatClient oldClient = chatClientCache.remove(configId);
            if (oldClient != null) {
                log.debug("清除旧的ChatClient缓存: {}", configId);
            }
            
            // 如果是当前活跃配置，立即创建新的ChatClient
            if (configId.equals(currentConfigurationId)) {
                ChatClient newClient = createChatClientForConfiguration(configId);
                chatClientCache.put(configId, newClient);
                log.info("当前活跃配置的ChatClient已重建: {}", configId);
            }
        }
        
        // 更新当前配置ID（如果配置被启用）
        if (event.getNewConfiguration().isEnabled()) {
            this.currentConfigurationId = configId;
            log.info("切换到新的活跃配置: {}", configId);
        }
    }
    
    /**
     * 根据配置ID创建ChatClient
     * @param configurationId 配置ID
     * @return ChatClient实例
     */
    private ChatClient createChatClientForConfiguration(String configurationId) {
        try {
            log.debug("为配置创建ChatClient: {}", configurationId);
            
            // TODO: 从配置仓储获取具体配置
            // 这里需要注入LlmConfigurationRepository来获取配置详情
            ModelConfiguration config = getConfigurationById(configurationId);
            
            // 根据配置创建OpenAI兼容的ChatModel
            ChatModel chatModel = createChatModel(config);
            
            // 创建ChatClient
            ChatClient chatClient = ChatClient.builder(chatModel).build();
            
            log.info("ChatClient创建成功: Provider={}, Model={}", 
                    config.getProvider().getDisplayName(),
                    config.getProvider().getModelName());
            
            return chatClient;
            
        } catch (Exception e) {
            log.error("创建ChatClient失败: {}", configurationId, e);
            throw new RuntimeException("ChatClient创建失败", e);
        }
    }
    
    /**
     * 根据LLM配置创建ChatModel
     * @param config LLM配置
     * @return ChatModel实例
     */
    private ChatModel createChatModel(ModelConfiguration config) {
        // 统一使用OpenAI兼容接口
        OpenAiApi openAiApi = new OpenAiApi(
                config.getProvider().getBaseUrl(),
                config.getProvider().getApiKey()
        );
        
        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .model(config.getProvider().getModelName())
                .maxTokens(config.getSettings().getMaxTokens())
                .temperature(config.getSettings().getTemperature().floatValue())
                .topP(config.getSettings().getTopP().floatValue())
                .frequencyPenalty(config.getSettings().getFrequencyPenalty().floatValue())
                .presencePenalty(config.getSettings().getPresencePenalty().floatValue())
                .build();
    }
    
    /**
     * 根据配置ID获取配置（临时实现）
     * TODO: 注入LlmConfigurationRepository
     */
    private ModelConfiguration getConfigurationById(String configurationId) {
        // 临时返回默认配置，实际应该从仓储获取
        return ModelConfiguration.createDefault();
    }
    
    /**
     * 清除所有缓存的ChatClient
     */
    public void clearAllCachedClients() {
        log.info("清除所有ChatClient缓存");
        chatClientCache.clear();
    }
}
