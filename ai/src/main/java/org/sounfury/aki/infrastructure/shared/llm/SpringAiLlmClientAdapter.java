package org.sounfury.aki.infrastructure.shared.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.shared.llm.LlmClient;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * Spring AI客户端适配器
 * 将领域层的LlmClient接口适配到Spring AI的ChatClient
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpringAiLlmClientAdapter implements LlmClient {

    private final org.sounfury.aki.infrastructure.llmconfig.DynamicChatClientManager chatClientManager;
    
    @Override
    public LlmResponse call(LlmRequest request) {
        try {
            log.debug("执行同步LLM调用，用户消息: {}", request.userMessage());

            // 获取当前动态ChatClient
            ChatClient currentChatClient = chatClientManager.getCurrentChatClient();

            // 构建ChatClient调用
            var promptBuilder = currentChatClient.prompt()
                    .user(request.userMessage());
            
            // 添加系统消息
            for (var systemMsg : request.systemMessages()) {
                promptBuilder.system(systemMsg.content());
            }
            
            // 添加工具（如果有）
            if (!request.availableTools().isEmpty()) {
                // TODO: 实现工具转换逻辑
                log.debug("检测到 {} 个可用工具", request.availableTools().size());
            }
            
            // 执行调用
            ChatResponse chatResponse = promptBuilder.call().chatResponse();
            String content = chatResponse.getResult().getOutput().getText();
            
            log.debug("LLM调用成功，响应长度: {}", content.length());
            
            return new LlmResponse(
                    content,
                    List.of(), // TODO: 解析工具调用
                    Map.of("model", chatResponse.getMetadata().getModel())
            );
            
        } catch (Exception e) {
            log.error("LLM调用失败", e);
            throw new RuntimeException("LLM调用失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Flux<String> stream(LlmRequest request) {
        try {
            log.debug("执行流式LLM调用，用户消息: {}", request.userMessage());

            // 获取当前动态ChatClient
            ChatClient currentChatClient = chatClientManager.getCurrentChatClient();

            // 构建ChatClient调用
            var promptBuilder = currentChatClient.prompt()
                    .user(request.userMessage());
            
            // 添加系统消息
            for (var systemMsg : request.systemMessages()) {
                promptBuilder.system(systemMsg.content());
            }
            
            // 执行流式调用
            return promptBuilder.stream().content()
                    .doOnComplete(() -> log.debug("流式LLM调用完成"))
                    .doOnError(error -> log.error("流式LLM调用失败", error));
            
        } catch (Exception e) {
            log.error("流式LLM调用失败", e);
            return Flux.error(new RuntimeException("流式LLM调用失败: " + e.getMessage(), e));
        }
    }
}
