package org.sounfury.aki.infrastructure.llm;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.model.aggregate.ModelConfiguration;
import org.sounfury.aki.domain.model.repository.LlmConfigurationRepository;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内存中的LLM配置仓储实现
 * 用于存储和管理LLM配置信息
 */
@Slf4j
@Repository
public class InMemoryLlmConfigurationRepository implements LlmConfigurationRepository {
    
    /**
     * 全局配置ID常量
     */
    private static final String GLOBAL_CONFIG_ID = "global-llm-config";
    
    /**
     * 配置存储映射
     */
    private final Map<String, ModelConfiguration> configurations = new ConcurrentHashMap<>();
    
    /**
     * 初始化默认配置
     */
    public InMemoryLlmConfigurationRepository() {
        // 初始化默认全局配置
        ModelConfiguration defaultConfig = ModelConfiguration.createDefault();
        configurations.put(GLOBAL_CONFIG_ID, defaultConfig);
        log.info("初始化默认LLM配置: {}", defaultConfig.getProvider().getDisplayName());
    }
    
    @Override
    public ModelConfiguration save(ModelConfiguration configuration) {
        if (configuration == null) {
            throw new IllegalArgumentException("配置不能为空");
        }
        
        String configId = configuration.getConfigurationId();
        configurations.put(configId, configuration);
        
        log.debug("保存LLM配置: ID={}, Provider={}, Model={}", 
                configId, 
                configuration.getProvider().getDisplayName(),
                configuration.getProvider().getModelName());
        
        return configuration;
    }
    
    @Override
    public Optional<ModelConfiguration> findById(String configurationId) {
        if (configurationId == null || configurationId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        ModelConfiguration config = configurations.get(configurationId.trim());
        log.debug("查找LLM配置: ID={}, Found={}", configurationId, config != null);
        
        return Optional.ofNullable(config);
    }
    
    @Override
    public Optional<ModelConfiguration> findGlobalConfiguration() {
        ModelConfiguration globalConfig = configurations.get(GLOBAL_CONFIG_ID);
        
        if (globalConfig == null) {
            log.warn("全局LLM配置不存在，创建默认配置");
            globalConfig = ModelConfiguration.createDefault();
            configurations.put(GLOBAL_CONFIG_ID, globalConfig);
        }
        
        log.debug("获取全局LLM配置: Provider={}, Model={}", 
                globalConfig.getProvider().getDisplayName(),
                globalConfig.getProvider().getModelName());
        
        return Optional.of(globalConfig);
    }
    
    @Override
    public void deleteById(String configurationId) {
        if (configurationId == null || configurationId.trim().isEmpty()) {
            return;
        }
        
        // 不允许删除全局配置
        if (GLOBAL_CONFIG_ID.equals(configurationId.trim())) {
            log.warn("尝试删除全局配置被拒绝: {}", configurationId);
            throw new IllegalArgumentException("不能删除全局配置");
        }
        
        ModelConfiguration removed = configurations.remove(configurationId.trim());
        log.debug("删除LLM配置: ID={}, Removed={}", configurationId, removed != null);
    }
    
    @Override
    public boolean existsById(String configurationId) {
        if (configurationId == null || configurationId.trim().isEmpty()) {
            return false;
        }
        
        boolean exists = configurations.containsKey(configurationId.trim());
        log.debug("检查LLM配置存在性: ID={}, Exists={}", configurationId, exists);
        
        return exists;
    }
    
    /**
     * 获取所有配置（用于调试和管理）
     */
    public Map<String, ModelConfiguration> getAllConfigurations() {
        return Map.copyOf(configurations);
    }
    
    /**
     * 获取配置数量
     */
    public int getConfigurationCount() {
        return configurations.size();
    }
    
    /**
     * 清空所有配置（除了全局配置）
     */
    public void clearNonGlobalConfigurations() {
        ModelConfiguration globalConfig = configurations.get(GLOBAL_CONFIG_ID);
        configurations.clear();
        
        if (globalConfig != null) {
            configurations.put(GLOBAL_CONFIG_ID, globalConfig);
        } else {
            // 重新创建默认全局配置
            configurations.put(GLOBAL_CONFIG_ID, ModelConfiguration.createDefault());
        }
        
        log.info("清空非全局LLM配置，保留全局配置");
    }
    
    /**
     * 重置为默认配置
     */
    public void resetToDefault() {
        configurations.clear();
        ModelConfiguration defaultConfig = ModelConfiguration.createDefault();
        configurations.put(GLOBAL_CONFIG_ID, defaultConfig);
        
        log.info("重置LLM配置为默认值: {}", defaultConfig.getProvider().getDisplayName());
    }
}
