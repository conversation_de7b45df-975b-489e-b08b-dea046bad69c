package org.sounfury.aki.infrastructure.shared.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.shared.llm.AdvisorFactory;
import org.sounfury.aki.domain.shared.llm.AdvisorRegistry;
import org.sounfury.aki.domain.shared.llm.LlmAdvisor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认Advisor工厂实现
 * 根据业务场景构建合适的Advisor链
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultAdvisorFactory implements AdvisorFactory {
    
    private final AdvisorRegistry advisorRegistry;
    
    @Override
    public List<LlmAdvisor> buildConversationAdvisors(ConversationContext context) {
        log.debug("构建对话Advisor链，角色ID: {}, 用户: {}", context.characterId(), context.userName());
        
        List<LlmAdvisor> advisors = new ArrayList<>();
        
        // 1. 系统提示词Advisor（最高优先级）
        advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.SYSTEM_PROMPT)
                .forEach(advisors::add);
        
        // 2. 角色相关Advisor
        if (context.characterId() != null && !context.characterId().isEmpty()) {
            advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.CHARACTER)
                    .forEach(advisors::add);
        }
        
        // 3. 记忆管理Advisor
        if (context.enableMemory()) {
            advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.MEMORY)
                    .forEach(advisors::add);
        }
        
        // 4. RAG检索Advisor
        if (context.enableRag()) {
            advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.RAG)
                    .forEach(advisors::add);
        }
        
        // 5. 工具调用Advisor（Agent模式）
        if (context.enableTools()) {
            advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.TOOL)
                    .forEach(advisors::add);
        }
        
        // 6. 行为指导Advisor
        advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.BEHAVIOR)
                .forEach(advisors::add);
        
        // 按优先级排序
        List<LlmAdvisor> sortedAdvisors = advisors.stream()
                .sorted((a1, a2) -> Integer.compare(a1.getOrder(), a2.getOrder()))
                .collect(Collectors.toList());
        
        log.debug("构建完成，共 {} 个对话Advisor", sortedAdvisors.size());
        return sortedAdvisors;
    }
    
    @Override
    public List<LlmAdvisor> buildTaskAdvisors(TaskContext context) {
        log.debug("构建任务Advisor链，任务类型: {}", context.taskType());
        
        List<LlmAdvisor> advisors = new ArrayList<>();
        
        // 1. 系统提示词Advisor
        advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.SYSTEM_PROMPT)
                .forEach(advisors::add);
        
        // 2. 角色相关Advisor（如果启用）
        if (context.enableCharacter() && context.characterId() != null) {
            advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.CHARACTER)
                    .forEach(advisors::add);
        }
        
        // 3. 行为指导Advisor（任务特定）
        advisorRegistry.getAdvisorsByType(AdvisorRegistry.AdvisorType.BEHAVIOR)
                .forEach(advisors::add);
        
        // 按优先级排序
        List<LlmAdvisor> sortedAdvisors = advisors.stream()
                .sorted((a1, a2) -> Integer.compare(a1.getOrder(), a2.getOrder()))
                .collect(Collectors.toList());
        
        log.debug("构建完成，共 {} 个任务Advisor", sortedAdvisors.size());
        return sortedAdvisors;
    }
    
    @Override
    public List<LlmAdvisor> buildCustomAdvisors(List<String> advisorNames, AdvisorContext context) {
        log.debug("构建自定义Advisor链，指定Advisor: {}", advisorNames);
        
        List<LlmAdvisor> advisors = advisorNames.stream()
                .map(advisorRegistry::getAdvisor)
                .filter(opt -> opt.isPresent())
                .map(opt -> opt.get())
                .sorted((a1, a2) -> Integer.compare(a1.getOrder(), a2.getOrder()))
                .collect(Collectors.toList());
        
        log.debug("构建完成，共 {} 个自定义Advisor", advisors.size());
        return advisors;
    }
}
