package org.sounfury.aki.domain.shared.llm;

import java.util.List;

/**
 * Advisor工厂接口
 * 根据业务场景构建合适的Advisor链
 */
public interface AdvisorFactory {
    
    /**
     * 为对话场景构建Advisor链
     * @param context 对话上下文信息
     * @return Advisor列表
     */
    List<LlmAdvisor> buildConversationAdvisors(ConversationContext context);
    
    /**
     * 为任务场景构建Advisor链
     * @param context 任务上下文信息
     * @return Advisor列表
     */
    List<LlmAdvisor> buildTaskAdvisors(TaskContext context);
    
    /**
     * 构建自定义Advisor链
     * @param advisorNames 指定的Advisor名称列表
     * @param context 通用上下文信息
     * @return Advisor列表
     */
    List<LlmAdvisor> buildCustomAdvisors(List<String> advisorNames, AdvisorContext context);
    
    /**
     * 对话上下文
     */
    record ConversationContext(
            String characterId,
            String userName,
            boolean enableMemory,
            boolean enableRag,
            boolean enableTools
    ) {}
    
    /**
     * 任务上下文
     */
    record TaskContext(
            String taskType,
            String characterId,
            boolean enableCharacter
    ) {}
    
    /**
     * 通用Advisor上下文
     */
    record AdvisorContext(
            String userId,
            String sessionId,
            java.util.Map<String, Object> parameters
    ) {}
}
