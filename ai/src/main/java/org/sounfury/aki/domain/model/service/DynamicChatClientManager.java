package org.sounfury.aki.domain.model.service;

import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.springframework.ai.chat.client.ChatClient;

/**
 * 动态ChatClient管理器接口
 * 根据LLM配置变更动态创建和管理ChatClient实例
 */
public interface DynamicChatClientManager {
    
    /**
     * 获取当前活跃的ChatClient
     * @return 当前配置对应的ChatClient实例
     */
    ChatClient getCurrentChatClient();
    
    /**
     * 根据指定配置获取ChatClient
     * @param configuration LLM配置
     * @return 对应的ChatClient实例
     */
    ChatClient getChatClient(LlmConfiguration configuration);
    
    /**
     * 刷新ChatClient（配置变更时调用）
     * @param newConfiguration 新的LLM配置
     */
    void refreshChatClient(LlmConfiguration newConfiguration);
    
    /**
     * 获取当前使用的LLM配置
     * @return 当前LLM配置
     */
    LlmConfiguration getCurrentConfiguration();
    
    /**
     * 检查指定配置的ChatClient是否已缓存
     * @param configurationId 配置ID
     * @return 是否已缓存
     */
    boolean isChatClientCached(String configurationId);
    
    /**
     * 清除所有缓存的ChatClient
     */
    void clearAllCachedClients();
}
