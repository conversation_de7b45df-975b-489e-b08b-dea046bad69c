package org.sounfury.aki.domain.shared.llm;

import org.springframework.core.Ordered;

/**
 * LLM Advisor抽象接口
 * 领域层定义的Advisor契约，用于增强LLM请求和响应处理
 * 简化设计，直接对应Spring AI的Advisor模式
 */
public interface LlmAdvisor extends Ordered {

    /**
     * 获取Advisor名称
     * @return Advisor唯一标识名称
     */
    String getName();

    /**
     * 获取执行顺序
     * 数值越小优先级越高，越早执行
     * @return 执行顺序
     */
    @Override
    default int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
