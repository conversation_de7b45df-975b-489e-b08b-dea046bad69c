package org.sounfury.aki.domain.shared.llm;

import java.util.List;
import java.util.Optional;

/**
 * Advisor注册器接口
 * 管理所有可用的Advisor实例
 */
public interface AdvisorRegistry {
    
    /**
     * 注册Advisor
     * @param advisor 要注册的Advisor
     */
    void register(LlmAdvisor advisor);
    
    /**
     * 批量注册Advisor
     * @param advisors 要注册的Advisor列表
     */
    void registerAll(List<LlmAdvisor> advisors);
    
    /**
     * 根据名称获取Advisor
     * @param name Advisor名称
     * @return Advisor实例，如果不存在则返回空
     */
    Optional<LlmAdvisor> getAdvisor(String name);
    
    /**
     * 根据类型获取Advisor
     * @param type Advisor类型
     * @return 匹配的Advisor列表
     */
    List<LlmAdvisor> getAdvisorsByType(AdvisorType type);
    
    /**
     * 获取所有已注册的Advisor
     * @return 所有Advisor列表
     */
    List<LlmAdvisor> getAllAdvisors();
    
    /**
     * 检查Advisor是否已注册
     * @param name Advisor名称
     * @return 如果已注册返回true，否则返回false
     */
    boolean isRegistered(String name);
    
    /**
     * 注销Advisor
     * @param name Advisor名称
     */
    void unregister(String name);
    
    /**
     * Advisor类型枚举
     */
    enum AdvisorType {
        SYSTEM_PROMPT,      // 系统提示词
        CHARACTER,          // 角色相关
        MEMORY,             // 记忆管理
        RAG,                // 知识检索
        TOOL,               // 工具调用
        BEHAVIOR,           // 行为指导
        CUSTOM              // 自定义
    }
}
