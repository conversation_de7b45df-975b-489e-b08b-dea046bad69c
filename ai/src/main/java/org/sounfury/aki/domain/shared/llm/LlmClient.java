package org.sounfury.aki.domain.shared.llm;

import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * LLM客户端抽象接口
 * 领域层定义的LLM调用契约，隔离具体的LLM框架实现
 * 支持Advisor模式进行请求/响应的增强处理
 */
public interface LlmClient {

    /**
     * 同步调用LLM
     * @param request LLM请求
     * @return LLM响应
     */
    LlmResponse call(LlmRequest request);

    /**
     * 流式调用LLM
     * @param request LLM请求
     * @return 流式响应
     */
    Flux<String> stream(LlmRequest request);


    
    /**
     * LLM请求对象
     */
    record LlmRequest(
            String userMessage,
            List<SystemMessage> systemMessages,
            List<Tool> availableTools,
            Map<String, Object> parameters
    ) {
        public static LlmRequest simple(String userMessage) {
            return new LlmRequest(userMessage, List.of(), List.of(), Map.of());
        }
        
        public static LlmRequest withSystem(String userMessage, String systemMessage) {
            return new LlmRequest(
                userMessage, 
                List.of(new SystemMessage(systemMessage)), 
                List.of(), 
                Map.of()
            );
        }
    }
    
    /**
     * 系统消息
     */
    record SystemMessage(String content) {}
    
    /**
     * 工具定义
     */
    record Tool(
            String name,
            String description,
            Map<String, Object> parameters
    ) {}
    
    /**
     * LLM响应对象
     */
    record LlmResponse(
            String content,
            List<ToolCall> toolCalls,
            Map<String, Object> metadata
    ) {
        public boolean hasToolCalls() {
            return toolCalls != null && !toolCalls.isEmpty();
        }
    }
    
    /**
     * 工具调用
     */
    record ToolCall(
            String toolName,
            Map<String, Object> arguments
    ) {}
}
