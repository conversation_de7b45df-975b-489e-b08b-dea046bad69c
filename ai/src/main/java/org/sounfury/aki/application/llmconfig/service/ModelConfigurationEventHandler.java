package org.sounfury.aki.application.llmconfig.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.llm.config.event.ModelConfigurationChangedEvent;
import org.sounfury.aki.infrastructure.llm.ConfigurableChatModelFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 模型配置变更事件处理器
 * 响应LLM配置变更事件，执行相应的处理逻辑
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ModelConfigurationEventHandler {
    
    private final ConfigurableChatModelFactory chatModelFactory;
    
    /**
     * 处理模型配置变更事件
     */
    @Async
    @EventListener
    public void handleModelConfigurationChanged(ModelConfigurationChangedEvent event) {
        log.info("接收到模型配置变更事件: {}", event.getEventSummary());
        
        try {
            // 根据变更类型执行不同的处理逻辑
            if (event.isCriticalChange()) {
                handleCriticalChange(event);
            } else if (event.isParameterAdjustment()) {
                handleParameterAdjustment(event);
            }
            
            log.info("模型配置变更事件处理完成: {}", event.getConfigurationId());
            
        } catch (Exception e) {
            log.error("处理模型配置变更事件失败: ConfigId={}, Error={}", 
                    event.getConfigurationId(), e.getMessage(), e);
        }
    }
    
    /**
     * 处理关键变更（需要重新创建模型实例）
     */
    private void handleCriticalChange(ModelConfigurationChangedEvent event) {
        String configId = event.getConfigurationId();
        
        log.info("处理关键配置变更，重新创建模型实例: ConfigId={}, ChangeType={}", 
                configId, event.getChangeType());
        
        try {
            // 移除旧的缓存模型实例
            chatModelFactory.removeCachedModel(configId);
            
            // 如果新配置有效且启用，预创建模型实例
            if (event.getNewConfiguration().isValid() && event.getNewConfiguration().isEnabled()) {
                chatModelFactory.createOrGetChatModel(event.getNewConfiguration());
                log.info("新模型实例创建成功: Provider={}, Model={}", 
                        event.getNewConfiguration().getProvider().getDisplayName(),
                        event.getNewConfiguration().getProvider().getModelName());
            } else {
                log.warn("新配置无效或已禁用，跳过模型实例创建: ConfigId={}", configId);
            }
            
        } catch (Exception e) {
            log.error("重新创建模型实例失败: ConfigId={}, Error={}", configId, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 处理参数调整（不需要重新创建模型实例）
     */
    private void handleParameterAdjustment(ModelConfigurationChangedEvent event) {
        String configId = event.getConfigurationId();
        
        log.info("处理参数调整，保持现有模型实例: ConfigId={}, ChangeType={}", 
                configId, event.getChangeType());
        

        log.debug("参数调整完成，新参数将在下次调用时生效: ConfigId={}", configId);
    }
    
    /**
     * 处理配置初始化事件
     */
    @EventListener
    public void handleConfigurationInitialization(ModelConfigurationChangedEvent event) {
        if (event.getChangeType() == ModelConfigurationChangedEvent.ChangeType.INITIALIZATION) {
            log.info("处理配置初始化事件: {}", event.getEventSummary());
            
            try {
                // 预创建默认模型实例
                if (event.getNewConfiguration().isValid() && event.getNewConfiguration().isEnabled()) {
                    chatModelFactory.createOrGetChatModel(event.getNewConfiguration());
                    log.info("默认模型实例初始化成功");
                }
            } catch (Exception e) {
                log.error("默认模型实例初始化失败: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 处理配置禁用事件
     */
    private void handleConfigurationDisabled(ModelConfigurationChangedEvent event) {
        if (!event.getNewConfiguration().isEnabled()) {
            String configId = event.getConfigurationId();
            log.info("配置已禁用，移除缓存的模型实例: ConfigId={}", configId);
            
            chatModelFactory.removeCachedModel(configId);
        }
    }
    
    /**
     * 获取事件处理统计信息
     */
    public String getEventHandlingStats() {
        return String.format("缓存的模型实例数量: %d", chatModelFactory.getCachedModelCount());
    }
}
