package org.sounfury.aki.application.llmConfig.dto;

import lombok.Builder;
import lombok.Data;
import org.sounfury.aki.domain.model.aggregate.ModelConfiguration;
import org.sounfury.aki.domain.llm.ModelCapability;

import java.time.LocalDateTime;
import java.util.List;

/**
 * LLM配置数据传输对象
 */
@Data
@Builder
public class LlmConfigurationDto {
    
    /**
     * 配置ID
     */
    private String configurationId;
    
    /**
     * 提供商类型
     */
    private String providerType;
    
    /**
     * 提供商显示名称
     */
    private String providerDisplayName;
    
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * API密钥（脱敏）
     */
    private String maskedApiKey;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 最大Token数
     */
    private Integer maxTokens;
    
    /**
     * 温度参数
     */
    private Double temperature;
    
    /**
     * Top-P参数
     */
    private Double topP;
    
    /**
     * 频率惩罚
     */
    private Double frequencyPenalty;
    
    /**
     * 存在惩罚
     */
    private Double presencePenalty;
    
    /**
     * 是否启用流式输出
     */
    private Boolean streamEnabled;
    
    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 模型支持的能力列表
     */
    private List<ModelCapabilityDto> capabilities;

    /**
     * 是否支持Function Call
     */
    private Boolean supportsFunctionCall;

    /**
     * 是否适合Agent模式
     */
    private Boolean suitableForAgent;
    
    /**
     * 从领域对象转换为DTO
     */
    public static LlmConfigurationDto fromDomain(ModelConfiguration configuration) {
        // 简化：只返回支持的能力
        List<ModelCapabilityDto> capabilities = List.of();
        if (configuration.getProvider().supportsFunctionCall()) {
            capabilities = List.of(ModelCapabilityDto.fromDomain(ModelCapability.FUNCTION_CALL));
        }
        if (configuration.getProvider().supportsMultimodal()) {
            capabilities = List.of(
                ModelCapabilityDto.fromDomain(ModelCapability.FUNCTION_CALL),
                ModelCapabilityDto.fromDomain(ModelCapability.MULTIMODAL)
            );
        }

        return LlmConfigurationDto.builder()
                .configurationId(configuration.getConfigurationId())
                .providerType(configuration.getProvider().getType().name())
                .providerDisplayName(configuration.getProvider().getDisplayName())
                .baseUrl(configuration.getProvider().getBaseUrl())
                .maskedApiKey(configuration.getProvider().getMaskedApiKey())
                .modelName(configuration.getProvider().getModelName())
                .maxTokens(configuration.getSettings().getMaxTokens())
                .temperature(configuration.getSettings().getTemperature())
                .topP(configuration.getSettings().getTopP())
                .frequencyPenalty(configuration.getSettings().getFrequencyPenalty())
                .presencePenalty(configuration.getSettings().getPresencePenalty())
                .streamEnabled(configuration.getSettings().isStreamEnabled())
                .timeoutSeconds(configuration.getSettings().getTimeoutSeconds())
                .retryCount(configuration.getSettings().getRetryCount())
                .enabled(configuration.isEnabled())
                .description(configuration.getDescription())
                .createdAt(configuration.getCreatedAt())
                .updatedAt(configuration.getUpdatedAt())
                .capabilities(capabilities)
                .supportsFunctionCall(configuration.getProvider().supportsFunctionCall())
                .suitableForAgent(configuration.getProvider().isSuitableForAgent())
                .build();
    }

    /**
     * 模型能力DTO
     */
    @Data
    @Builder
    public static class ModelCapabilityDto {
        private String code;
        private String name;
        private String description;

        public static ModelCapabilityDto fromDomain(ModelCapability capability) {
            return ModelCapabilityDto.builder()
                    .code(capability.getCode())
                    .name(capability.getName())
                    .description(capability.getDescription())
                    .build();
        }
    }
}
