# 项目上下文信息

- 用户要求基于需求.md重新进行DDD建模，按照子域划分->限界上下文->上下文映射的流程重构AI模块。当前AI模块存在严重的DDD设计问题：1)没有限界上下文划分 2)领域层直接依赖Spring AI等外部框架 3)按功能而非业务能力划分 4)缺乏适配层 5)聚合边界不清晰等问题
  
- 🎯 对话上下文 (Conversation Context) 的领域事件
  会话相关事件
  ConversationSessionStarted - 会话开始
  ConversationSessionEnded - 会话结束
  ConversationSessionExpired - 会话过期
  对话相关事件
  MessageReceived - 收到用户消息
  MessageGenerated - AI生成回复消息
  ToolInvocationRequested - 请求工具调用（Agent模式）
  ToolInvocationCompleted - 工具调用完成
  记忆相关事件
  ImportantMemoryCreated - 创建重点记忆
  ConversationMemoryUpdated - 对话记忆更新

- 🔧 任务执行上下文 (Task Context) 的领域事件
  任务生命周期事件
  TaskTriggered - 任务被触发
  TaskStarted - 任务开始执行
  TaskCompleted - 任务执行完成
  TaskFailed - 任务执行失败
  特定任务事件
  ArticleSummarized - 文章总结完成
  ArticleExcerpted - 文章摘录完成
  PublishCongratulationSent - 发布祝贺发送完成
 
- 👤 角色管理上下文 (Character Context) 的领域事件
  CharacterCreated - 角色创建
  CharacterUpdated - 角色更新
  CharacterActivated - 角色激活
  CharacterDeactivated - 角色停用
  
- 📝 提示词模板上下文 (Prompt Template Context) 的领域事件
  PromptTemplateCreated - 提示词模板创建
  PromptTemplateUpdated - 提示词模板更新
  GlobalPromptUpdated - 全局提示词更新
  PromptRendered - 提示词渲染完成
  
- 📚 知识库上下文 (Knowledge Context) 的领域事件
  KnowledgeEntryCreated - 知识条目创建
  KnowledgeEntryTriggered - 知识条目被触发
  WorldBookActivated - 世界书激活
  RAGSearchPerformed - RAG检索执行

- ⚙️ 模型配置上下文 (Model Context) 的领域事件
  ModelConfigurationChanged - 模型配置变更
  ModelProviderSwitched - 模型提供商切换
  
- 🔄 跨上下文协作的事件流
  典型的对话流程事件链：
  ConversationSessionStarted → 触发角色加载、记忆恢复
  MessageReceived → 触发知识库检索、提示词构建
  PromptRendered → 触发模型调用
  MessageGenerated → 触发记忆更新
  典型的任务执行事件链：
  TaskTriggered → 触发提示词模板加载
  PromptRendered → 触发模型调用
  TaskCompleted → 可能触发通知或后续动作